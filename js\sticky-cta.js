// sticky-cta.js — handles sticky CTA visibility and behavior
(function () {
  function setupStickyCta() {
    const stickyCta = document.getElementById('sticky-cta');
    const heroSection = document.getElementById('hero');
    const contactSection = document.getElementById('contact');
    
    if (!stickyCta || !heroSection || !contactSection) return;

    function updateStickyVisibility() {
      const heroBottom = heroSection.offsetTop + heroSection.offsetHeight;
      const contactTop = contactSection.offsetTop;
      const scrollY = window.scrollY;
      
      // Show sticky CTA after hero section and before contact section
      const shouldShow = scrollY > heroBottom && scrollY < contactTop - 200;
      stickyCta.classList.toggle('visible', shouldShow);
    }

    // Check on scroll
    window.addEventListener('scroll', updateStickyVisibility, { passive: true });
    
    // Check on resize
    window.addEventListener('resize', updateStickyVisibility, { passive: true });
    
    // Initial check
    updateStickyVisibility();
  }

  // Initialize after sections load
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", setupStickyCta);
  } else {
    setupStickyCta();
  }
  window.addEventListener("sections:loaded", setupStickyCta);
})();
