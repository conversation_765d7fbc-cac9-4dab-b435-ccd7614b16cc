// nav.js — handles navbar CTAs & smooth scroll
document.addEventListener("DOMContentLoaded", () => {
  const heroCta = document.getElementById("hero-cta");
  const bottomCta = document.getElementById("bottom-cta");

  function scrollToForm() {
    const formEl = document.querySelector("#contact");
    if (formEl) {
      formEl.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }

  if (heroCta) heroCta.addEventListener("click", scrollToForm);
  if (bottomCta) bottomCta.addEventListener("click", scrollToForm);
});
