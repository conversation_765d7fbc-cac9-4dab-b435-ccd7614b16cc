// nav.js — handles navbar CTAs & smooth scroll with dynamic sections
(function () {
  function scrollToContact(e) {
    if (e) e.preventDefault();
    const el = document.getElementById("contact");
    if (el) el.scrollIntoView({ behavior: "smooth", block: "start" });
  }

  function bindCtas() {
    // Known IDs
    ["hero-cta", "bottom-cta"].forEach((id) => {
      const btn = document.getElementById(id);
      if (btn && !btn.dataset.bound) {
        btn.addEventListener("click", scrollToContact);
        btn.dataset.bound = "true";
      }
    });
    // Any anchor pointing to #contact
    document.querySelectorAll('a[href="#contact"]').forEach((a) => {
      if (!a.dataset.bound) {
        a.addEventListener("click", scrollToContact);
        a.dataset.bound = "true";
      }
    });
    // Any element with data-scroll-to="contact"
    document.querySelectorAll('[data-scroll-to="contact"]').forEach((el) => {
      if (!el.dataset.bound) {
        el.addEventListener("click", scrollToContact);
        el.dataset.bound = "true";
      }
    });
  }

  // Bind when DOM is ready and again after sections load
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", bindCtas);
  } else {
    bindCtas();
  }
  window.addEventListener("sections:loaded", bindCtas);
})();
