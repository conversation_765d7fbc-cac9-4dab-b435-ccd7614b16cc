/* Base Styles — base.css */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: 'Inter', system-ui, sans-serif;
  background: linear-gradient(180deg, var(--bg), #eef8ff);
  color: #0f172a;
  line-height: 1.45;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: var(--primary-600);
  text-decoration: none;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

h1, h2, h3 {
  margin-top: 0;
}

ul {
  padding-left: 18px;
}

.small {
  font-size: var(--fs-xs);
}

.muted {
  color: var(--muted);
}
