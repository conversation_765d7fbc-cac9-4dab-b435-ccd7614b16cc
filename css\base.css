/* Base Styles — base.css */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: 'Inter', system-ui, sans-serif;
  background: linear-gradient(180deg, var(--bg), #eef8ff);
  color: #0f172a;
  line-height: 1.45;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: var(--primary-600);
  text-decoration: none;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

h1, h2, h3 {
  margin-top: 0;
  line-height: 1.2;
  font-weight: 700;
}

h1 {
  font-size: var(--fs-2xl);
  margin-bottom: var(--space-md);
}

h2 {
  font-size: var(--fs-xl);
  margin-bottom: var(--space-sm);
}

h3 {
  font-size: var(--fs-lg);
  margin-bottom: var(--space-xs);
}

p {
  margin-bottom: var(--space-md);
  line-height: 1.6;
}

.lead {
  font-size: 18px;
  line-height: 1.6;
  color: #374151;
  margin-bottom: var(--space-lg);
}

ul {
  padding-left: 18px;
}

.small {
  font-size: var(--fs-xs);
}

.muted {
  color: var(--muted);
}
