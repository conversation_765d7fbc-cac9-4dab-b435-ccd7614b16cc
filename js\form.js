// form.js — handles form validation & fake submission
document.addEventListener("DOMContentLoaded", () => {
  const form = document.getElementById("leadForm");
  const submitBtn = document.getElementById("submitBtn");
  const thanks = document.getElementById("thanks");

  if (!form) return;

  function simulateSubmission() {
    submitBtn.disabled = true;
    submitBtn.textContent = "Sending...";
    setTimeout(() => {
      submitBtn.disabled = false;
      submitBtn.textContent = "Request Trial";
      form.classList.add("hidden");
      thanks.classList.remove("hidden");
      window.scrollTo({ top: thanks.offsetTop - 80, behavior: "smooth" });
    }, 1200);
  }

  form.addEventListener("submit", (e) => {
    e.preventDefault();
    const name = document.getElementById("name").value.trim();
    const email = document.getElementById("email").value.trim();
    if (!name || !email) {
      alert("Please enter your name and a valid email.");
      return;
    }
    simulateSubmission();
  });
});
