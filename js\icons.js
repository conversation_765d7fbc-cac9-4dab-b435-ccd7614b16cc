// icons.js — SVG icon definitions and utilities
const Icons = {
  audience: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
    <circle cx="9" cy="7" r="4"/>
    <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
    <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
  </svg>`,
  
  quality: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <path d="M9 12l2 2 4-4"/>
    <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
    <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
    <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"/>
    <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"/>
  </svg>`,
  
  compliance: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
    <circle cx="12" cy="16" r="1"/>
    <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
  </svg>`,
  
  reporting: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <line x1="18" y1="20" x2="18" y2="10"/>
    <line x1="12" y1="20" x2="12" y2="4"/>
    <line x1="6" y1="20" x2="6" y2="14"/>
  </svg>`,
  
  sla: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <circle cx="12" cy="12" r="10"/>
    <polyline points="12,6 12,12 16,14"/>
  </svg>`,
  
  pricing: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <line x1="12" y1="1" x2="12" y2="23"/>
    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
  </svg>`
};

// Utility to replace icon placeholders with SVGs
function replaceIconPlaceholders() {
  const iconMap = {
    'A': 'audience',
    'Q': 'quality', 
    'C': 'compliance',
    'R': 'reporting',
    'S': 'sla',
    'P': 'pricing'
  };

  document.querySelectorAll('.ficon').forEach(icon => {
    const letter = icon.textContent.trim();
    if (iconMap[letter] && Icons[iconMap[letter]]) {
      icon.innerHTML = Icons[iconMap[letter]];
      icon.classList.add('svg-icon');
    }
  });
}

// Initialize after sections load
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", replaceIconPlaceholders);
} else {
  replaceIconPlaceholders();
}
window.addEventListener("sections:loaded", replaceIconPlaceholders);
