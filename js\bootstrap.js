// bootstrap.js — loads HTML partials and emits a ready event
(function () {
  const container = document.getElementById("main-container");
  if (!container) return;

  const sections = [
    "partials/header.html",
    "partials/hero.html",
    "partials/features.html",
    "partials/pricing.html",
    "partials/faq.html",
    "partials/contact.html",
    "partials/footer.html"
  ];

  async function loadSections() {
    for (const file of sections) {
      try {
        const res = await fetch(file);
        if (!res.ok) throw new Error(`${res.status} ${res.statusText}`);
        const html = await res.text();
        const wrapper = document.createElement("div");
        wrapper.innerHTML = html;
        container.appendChild(wrapper);
      } catch (err) {
        console.error(`Error loading ${file}:`, err);
      }
    }
    // After all sections are in the DOM, emit event for other scripts to hook into
    window.dispatchEvent(new CustomEvent("sections:loaded"));

    // Footer enhancements: set current year if placeholder exists
    const yearEl = document.getElementById("current-year");
    if (yearEl) {
      yearEl.textContent = String(new Date().getFullYear());
    }
  }

  // Start loading after DOM is parsed
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", loadSections);
  } else {
    loadSections();
  }
})();

