/* Components — components.css */

/* Navigation */
.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 12px;
  background: linear-gradient(180deg, rgba(255,255,255,0.85), rgba(255,255,255,0.7));
  box-shadow: var(--shadow);
}

/* Nav link styles */
nav ul li a {
  color: inherit;
  padding: 6px 8px;
  border-radius: 8px;
}

nav ul li a.active {
  background: rgba(11,132,255,0.10);
  color: var(--primary-600);
}


.brand {
  display: flex;
  gap: 12px;
  align-items: center;
}

.logo {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  background: linear-gradient(135deg, var(--primary), var(--accent));
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: 700;
  box-shadow: 0 6px 18px rgba(46, 160, 246, 0.2);
}

.logo small {
  font-size: 11px;
  opacity: 0.9;
}

/* Hero */
.hero {
  display: grid;
  grid-template-columns: 1fr 420px;
  gap: 28px;
  align-items: center;
}

.hero-card {
  background: var(--card);
  padding: 28px;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
}

.tag {
  display: inline-block;
  background: rgba(11,132,255,0.12);
  color: var(--primary-600);
  padding: 6px 10px;
  border-radius: 999px;
  font-weight: 600;
  margin-bottom: 12px;
}

.badges {
  display: flex;
  gap: 10px;
  margin-top: 14px;
}

.badge {
  background: linear-gradient(180deg,#f8fdff,#eef9ff);
  padding: 8px 12px;
  border-radius: 10px;
  border: 1px solid rgba(14,30,60,0.04);
  font-weight: 600;
}

/* Features */
.grid {
  display: grid;
  grid-template-columns: repeat(2,1fr);
  gap: 14px;
}

.feature {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 12px;
  background: linear-gradient(180deg, rgba(250,253,255,0.9), rgba(245,250,255,0.6));
  border: 1px solid rgba(14,30,60,0.03);
}

.ficon {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  background: linear-gradient(135deg, var(--primary), var(--accent));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
}

/* CTA Buttons */
.cta {
  background: linear-gradient(90deg, var(--primary), var(--primary-600));
  color: white;
  padding: 10px 14px;
  border-radius: 10px;
  font-weight: 600;
  border: none;
  cursor: pointer;
}

.ghost {
  background: transparent;
  border: 1px solid rgba(14,30,60,0.06);
  padding: 8px 12px;
  border-radius: 10px;
}

/* Testimonials */
.testimonials {
  display: grid;
  grid-template-columns: repeat(3,1fr);
  gap: 12px;
  margin-top: 14px;
}

.testimonial {
  background: var(--card);
  padding: 14px;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(9,30,63,0.03);
}

/* Pricing */
.pricing-container {
  display: flex;
  gap: 12px;
}

.plan {
  flex: 1;
  padding: 18px;
  border-radius: 12px;
  background: linear-gradient(180deg,#fff,#f5fbff);
  border: 1px solid rgba(14,30,60,0.03);
}

/* Pills */
.pill, .pill-small {
  display: inline-block;
  border-radius: 999px;
  font-weight: 700;
}

.pill {
  background: rgba(11,132,255,0.08);
  padding: 8px 12px;
  color: var(--primary-600);
}

.pill-small {
  padding: 6px 8px;
  background: rgba(14,30,60,0.04);
}

/* Calendly Embed */
.calendly-container {
  margin-top: 20px;
}
