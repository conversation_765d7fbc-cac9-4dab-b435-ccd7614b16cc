/* Components — components.css */

/* Sticky Header */
.header-sticky {
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 0 28px;
  background: rgba(246, 251, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(14,30,60,0.05);
}

/* Navigation */
.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 12px;
  background: linear-gradient(180deg, rgba(255,255,255,0.85), rgba(255,255,255,0.7));
  box-shadow: var(--shadow);
}

/* Nav link styles */
nav ul li a {
  color: inherit;
  padding: 6px 8px;
  border-radius: 8px;
}

nav ul li a.active {
  background: rgba(11,132,255,0.10);
  color: var(--primary-600);
}

/* Mobile Menu */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  gap: 4px;
}

.mobile-menu-toggle span {
  width: 20px;
  height: 2px;
  background: #0f172a;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.nav-mobile {
  display: none;
  background: var(--card);
  border-radius: 0 0 12px 12px;
  box-shadow: var(--shadow);
  margin-top: 8px;
}

.nav-mobile ul {
  list-style: none;
  padding: 12px 0;
  margin: 0;
}

.nav-mobile ul li {
  padding: 0;
}

.nav-mobile ul li a {
  display: block;
  padding: 12px 24px;
  color: inherit;
  text-decoration: none;
  border-radius: 0;
}

.nav-mobile ul li a:hover {
  background: rgba(11,132,255,0.05);
}

.nav-mobile.open {
  display: block;
}


.brand {
  display: flex;
  gap: 12px;
  align-items: center;
}

.logo {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  background: linear-gradient(135deg, var(--primary), var(--accent));
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: 700;
  box-shadow: 0 6px 18px rgba(46, 160, 246, 0.2);
}

.logo small {
  font-size: 11px;
  opacity: 0.9;
}

/* Hero */
.hero {
  display: grid;
  grid-template-columns: 1fr 420px;
  gap: 28px;
  align-items: center;
}

.hero-card {
  background: var(--card);
  padding: 28px;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
}

.tag {
  display: inline-block;
  background: rgba(11,132,255,0.12);
  color: var(--primary-600);
  padding: 6px 10px;
  border-radius: 999px;
  font-weight: 600;
  margin-bottom: 12px;
}

.badges {
  display: flex;
  gap: 10px;
  margin-top: 14px;
}

.badge {
  background: linear-gradient(180deg,#f8fdff,#eef9ff);
  padding: 8px 12px;
  border-radius: 10px;
  border: 1px solid rgba(14,30,60,0.04);
  font-weight: 600;
}

/* Features */
.grid {
  display: grid;
  grid-template-columns: repeat(2,1fr);
  gap: 14px;
}

.feature {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 12px;
  background: linear-gradient(180deg, rgba(250,253,255,0.9), rgba(245,250,255,0.6));
  border: 1px solid rgba(14,30,60,0.03);
}

.ficon {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  background: linear-gradient(135deg, var(--primary), var(--accent));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
}

.ficon.svg-icon {
  padding: 10px;
}

.ficon svg {
  width: 100%;
  height: 100%;
  stroke: white;
}

/* CTA Buttons */
.cta {
  background: linear-gradient(90deg, var(--primary), var(--primary-600));
  color: white;
  padding: 12px 24px;
  border-radius: 10px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  font-size: var(--fs-md);
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(46, 160, 246, 0.3);
}

.cta:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(46, 160, 246, 0.4);
}

.ghost {
  background: transparent;
  border: 1px solid rgba(14,30,60,0.12);
  padding: 12px 24px;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--primary-600);
}

.ghost:hover {
  background: rgba(46, 160, 246, 0.05);
  border-color: var(--primary);
}

/* Testimonials */
.testimonials {
  display: grid;
  grid-template-columns: repeat(3,1fr);
  gap: 12px;
  margin-top: 14px;
}

.testimonial {
  background: var(--card);
  padding: 14px;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(9,30,63,0.03);
}

/* Pricing */
.pricing-container {
  display: flex;
  gap: 12px;
}

.plan {
  flex: 1;
  padding: 18px;
  border-radius: 12px;
  background: linear-gradient(180deg,#fff,#f5fbff);
  border: 1px solid rgba(14,30,60,0.03);
}

/* Pills */
.pill, .pill-small {
  display: inline-block;
  border-radius: 999px;
  font-weight: 700;
}

.pill {
  background: rgba(11,132,255,0.08);
  padding: 8px 12px;
  color: var(--primary-600);
}

.pill-small {
  padding: 6px 8px;
  background: rgba(14,30,60,0.04);
}

/* Trust Indicators */
.trust-indicators {
  display: flex;
  gap: 16px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.trust-item {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(22, 163, 74, 0.05);
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid rgba(22, 163, 74, 0.1);
}

.trust-icon {
  font-size: 14px;
}

.trust-text {
  font-size: var(--fs-xs);
  font-weight: 600;
  color: var(--success);
}

/* Social Proof */
.social-proof {
  background: linear-gradient(180deg, rgba(255,255,255,0.5), rgba(245,250,255,0.8));
  border-radius: var(--radius);
  padding: 24px;
  margin: 40px 0;
}

.company-logos {
  display: flex;
  gap: 24px;
  justify-content: center;
  margin: 16px 0;
  flex-wrap: wrap;
}

.logo-item {
  padding: 8px 16px;
  background: rgba(14,30,60,0.03);
  border-radius: 8px;
  font-weight: 600;
  color: var(--muted);
  font-size: var(--fs-sm);
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 24px;
}

.stat {
  text-align: center;
  padding: 16px;
  background: var(--card);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(9,30,63,0.02);
}

.stat-number {
  font-size: var(--fs-xl);
  font-weight: 800;
  color: var(--primary-600);
  margin-bottom: 4px;
}

.stat-label {
  font-size: var(--fs-xs);
  color: var(--muted);
  font-weight: 500;
}

/* Sticky CTA */
.sticky-cta {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(14,30,60,0.1);
  padding: 12px 20px;
  z-index: 50;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.sticky-cta.visible {
  transform: translateY(0);
}

.sticky-cta-content {
  max-width: var(--maxw);
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.sticky-cta-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.sticky-cta-text .small {
  color: var(--muted);
}

/* FAQ Improvements */
.faq-grid {
  display: grid;
  gap: 16px;
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  background: var(--card);
  border-radius: 12px;
  border: 1px solid rgba(14,30,60,0.05);
  overflow: hidden;
}

.faq-item summary {
  padding: 20px;
  cursor: pointer;
  font-weight: 600;
  background: linear-gradient(180deg, rgba(255,255,255,1), rgba(250,253,255,0.8));
  border-bottom: 1px solid rgba(14,30,60,0.05);
  transition: background 0.2s ease;
}

.faq-item summary:hover {
  background: rgba(46, 160, 246, 0.02);
}

.faq-answer {
  padding: 20px;
  line-height: 1.6;
  color: #374151;
}

/* Calendly Embed */
.calendly-container {
  margin-top: 20px;
}
