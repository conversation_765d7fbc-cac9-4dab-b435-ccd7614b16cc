/* Layout — layout.css */
.container {
  max-width: var(--maxw);
  margin: 0 auto;
  padding: 28px;
}

.section {
  margin: 28px 0;
}

.center {
  text-align: center;
}

.row {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Responsive Layouts */
@media (max-width: 980px) {
  nav ul {
    display: none;
  }
}

@media (max-width: 560px) {
  .container {
    padding: 18px;
  }
  h1 {
    font-size: var(--fs-lg);
  }
}
