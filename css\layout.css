/* Layout — layout.css */
.container {
  max-width: var(--maxw);
  margin: 0 auto;
  padding: 28px;
}

.section {
  margin: var(--space-2xl) 0;
}

.section-header {
  margin-bottom: var(--space-xl);
}

.center {
  text-align: center;
}

.row {
  display: flex;
  gap: var(--space-sm);
  align-items: center;
}

.cta-row {
  margin-top: var(--space-lg);
  gap: var(--space-md);
}

/* Responsive Layouts */
@media (max-width: 980px) {
  .nav-desktop {
    display: none;
  }
  .mobile-menu-toggle {
    display: flex;
  }
  .header-sticky {
    padding: 0 18px;
  }
}

@media (max-width: 768px) {
  .hero {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  .testimonials {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  .pricing-container {
    flex-direction: column;
  }
  .grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 560px) {
  .container {
    padding: 18px;
  }
  h1 {
    font-size: var(--fs-lg);
  }
  .badges {
    flex-wrap: wrap;
  }
  .row {
    flex-direction: column;
    gap: 8px;
  }
  .trust-indicators {
    justify-content: center;
  }
  .sticky-cta-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  .company-logos {
    gap: 12px;
  }
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
}
