// scrollspy.js — highlights current section in nav (after dynamic load)
(function () {
  function setupScrollSpy() {
    const sections = Array.from(document.querySelectorAll("section[id]"));
    const navLinks = Array.from(document.querySelectorAll("nav ul li a"));
    if (!sections.length || !navLinks.length) return;

    function onScroll() {
      const scrollPos = window.scrollY + 120;
      for (const sec of sections) {
        if (scrollPos >= sec.offsetTop && scrollPos < sec.offsetTop + sec.offsetHeight) {
          const activeId = sec.getAttribute("id");
          navLinks.forEach((link) => {
            link.classList.toggle("active", link.getAttribute("href") === `#${activeId}`);
          });
          break;
        }
      }
    }

    window.removeEventListener("scroll", onScroll);
    window.addEventListener("scroll", onScroll, { passive: true });
    onScroll();
  }

  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", setupScrollSpy);
  } else {
    setupScrollSpy();
  }
  window.addEventListener("sections:loaded", setupScrollSpy);
})();
