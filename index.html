<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>LeadBaseAI — Trusted AI Outbound Lead Gen</title>
  <meta name="description" content="Ethical AI-powered outbound lead generation for WhatsApp, Instagram, and Email. Transparent SLAs, GDPR-compliant, and predictable results." />

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800&display=swap" rel="stylesheet" />

  <!-- Styles -->
  <link rel="stylesheet" href="css/variables.css" />
  <link rel="stylesheet" href="css/base.css" />
  <link rel="stylesheet" href="css/layout.css" />
  <link rel="stylesheet" href="css/components.css" />
</head>
<body>
  <div class="container" id="main-container">
    <!-- Sections will be loaded here dynamically -->
  </div>

  <footer class="section center">
    <p class="small muted">© LeadBaseAI — Built with trust. 
      <a href="#">Terms</a> · <a href="#">Privacy</a>
    </p>
  </footer>

  <!-- Scripts -->
  <script src="js/nav.js"></script>
  <script src="js/form.js"></script>
  <script src="js/scrollspy.js"></script>
  <script>
    // Dynamically load HTML partials
    const sections = [
      "partials/header.html",
      "partials/hero.html",
      "partials/features.html",
      "partials/pricing.html",
      "partials/faq.html",
      "partials/contact.html"
    ];

    const container = document.getElementById("main-container");

    async function loadSections() {
      for (const file of sections) {
        try {
          const res = await fetch(file);
          const html = await res.text();
          const wrapper = document.createElement("div");
          wrapper.innerHTML = html;
          container.appendChild(wrapper);
        } catch (err) {
          console.error(`Error loading ${file}:`, err);
        }
      }
    }

    loadSections();
  </script>
</body>
</html>
